- photos
    - input
        - saved to local storage, cleared from internal storage after processing (?)
    - output
        - saved to output bucket, deleted after 24 hours with bucket policy
    - preview
        - right now: not saved anywhere, expires after 1hr
- videos
    - input
        - saved to input bucket, deleted after transcoding finishes, backup 7 day deletion policy on input bucket
    - output
        - saved to output bucket, deleted after 24 hours with bucket policy
    - preview
        - right now: not saved anywhere, expires after 1hr 
- overall TODO:
    - save preview image along with job so it doesn't expire
    - show that preview in the job history
    - delete job AND preview 24 hours after completion (can't do a basic 24 hour policy on the bucket because of this constraint)
    - refund and delete stale jobs (and associated preview) (haven't finished processing within 24 hours)